#!/bin/bash

# Reverse-number files in a directory
#
# This script renames files beginning with "PacPrem" that contain 5-digit serial numbers,
# reversing the numbering sequence. For example:
# PacPrem00001.pdf becomes PacPrem00012.pdf
# PacPrem_00001.pdf becomes PacPrem_00012.pdf
# PacPremium00005.txt becomes PacPremium00008.txt
#
# Usage:
# ./reindex.sh         # Rename files
# ./reindex.sh -n      # Dry run (preview changes)
#
# Requirements:
# - Files must begin with "PacPrem"
# - Must contain a 5-digit serial number
# - Run in directory containing target files

# Function to print usage
usage() {
    echo "Usage: $0 [-n|--dry-run] [-h|--help]"
    echo "  -n, --dry-run   Show what would be renamed without making changes"
    echo "  -h, --help      Show this help message"
    exit 1
}

# Default to actual renaming
dry_run=false

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--dry-run)
            dry_run=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "Unknown option: $1"
            usage
            ;;
    esac
done

# Check if files beginning with <PERSON><PERSON><PERSON> exist
if ! ls PacPrem* 1> /dev/null 2>&1; then
    echo "Error: No files found beginning with 'PacPrem'"
    exit 1
fi

# Get all matching files and extract serial numbers
declare -a files=()
declare -a serials=()
declare -a extensions=()

for file in PacPrem*; do
    # Skip directories
    if [[ -d "$file" ]]; then
        echo "Warning: Skipping directory $file"
        continue
    fi

    # Extract serial number from any file beginning with PacPrem
    # Look for 5-digit numbers anywhere in the filename
    if [[ "$file" =~ ([0-9]{5}) ]]; then
        serial="${BASH_REMATCH[1]}"
        # Extract everything after the serial number as extension/suffix
        extension="${file#*$serial}"
        files+=("$file")
        serials+=("$serial")
        extensions+=("$extension")
    else
        echo "Warning: Skipping file $file - no 5-digit serial number found"
    fi
done

# Check if we have any valid files
if [ ${#files[@]} -eq 0 ]; then
    echo "Error: No valid files found with 5-digit serial numbers"
    exit 1
fi

# Sort serials to find min and max
sorted_serials=($(printf '%s\n' "${serials[@]}" | sort -n))
min_serial=${sorted_serials[0]}
max_serial=${sorted_serials[-1]}

echo "Found ${#files[@]} files with serials from $min_serial to $max_serial"

# Create temporary directory for safe renaming
temp_dir=$(mktemp -d)
trap "rm -rf '$temp_dir'" EXIT

if [ "$dry_run" = true ]; then
    echo "Dry run - proposed renames:"
fi

# Perform renaming in two phases to avoid conflicts
# Phase 1: Move all files to temporary names
for i in "${!files[@]}"; do
    file="${files[$i]}"
    serial="${serials[$i]}"
    extension="${extensions[$i]}"

    # Calculate reversed serial
    reversed_serial=$(printf "%05d" $((min_serial + max_serial - serial)))

    # Create new filename preserving the part before serial and extension after
    prefix="${file%$serial*}"
    new_filename="${prefix}${reversed_serial}${extension}"
    temp_filename="$temp_dir/$(basename "$file")"

    if [ "$dry_run" = true ]; then
        echo "$file -> $new_filename"
    else
        # Move to temp directory first
        if ! mv "$file" "$temp_filename"; then
            echo "Error: Failed to move $file to temporary location"
            exit 1
        fi
        echo "$file staged for rename to $new_filename"
    fi
done

# Phase 2: Move files back with new names (only if not dry run)
if [ "$dry_run" = false ]; then
    echo "Applying final renames..."
    for i in "${!files[@]}"; do
        file="${files[$i]}"
        serial="${serials[$i]}"
        extension="${extensions[$i]}"
        reversed_serial=$(printf "%05d" $((min_serial + max_serial - serial)))
        prefix="${file%$serial*}"
        new_filename="${prefix}${reversed_serial}${extension}"
        temp_filename="$temp_dir/$(basename "$file")"

        if ! mv "$temp_filename" "$new_filename"; then
            echo "Error: Failed to rename to $new_filename"
            exit 1
        else
            echo "✓ $file -> $new_filename"
        fi
    done
    echo "All files renamed successfully!"
fi