#!/bin/bash

# Find all PDF files with the TAX4825_ prefix
for file in PacPrem_*.pdf; do
    # Extract the current serial number
    current_serial=$(echo "$file" | grep -oP '(?<=TAX4825_)\d{5}')
    
    # Calculate the reversed serial number
    total_files=$(ls TAX4825_*.pdf | wc -l)
    reversed_serial=$(printf "%05d" $((total_files + 1 - current_serial)))
    
    # Create the new filename
    new_filename="${file//$current_serial/R$reversed_serial}"
    
    # Rename the file
    mv "$file" "$new_filename"
done
