#!/bin/bash

# Function to print usage
usage() {
    echo "Usage: $0 [-n|--dry-run] [-h|--help]"
    echo "  -n, --dry-run   Show what would be renamed without making changes"
    echo "  -h, --help      Show this help message"
    exit 1
}

# Default to actual renaming
dry_run=false

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--dry-run)
            dry_run=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "Unknown option: $1"
            usage
            ;;
    esac
done

# Check if PDF files exist
if ! ls PacPrem_*.pdf 1> /dev/null 2>&1; then
    echo "Error: No PDF files found matching PacPrem_*.pdf"
    exit 1
fi

# Count total files
total_files=$(ls PacPrem_*.pdf | wc -l)

# Temporary array to store renames
declare -a renames=()

# Collect rename operations
for file in PacPrem_*.pdf; do
    # Extract the current serial number
    if ! current_serial=$(echo "$file" | grep -oP '(?<=PacPrem_)\d{5}'); then
        echo "Warning: Skipping file $file - cannot extract serial number"
        continue
    fi

    # Validate serial number is numeric
    if [[ ! "$current_serial" =~ ^[0-9]{5}$ ]]; then
        echo "Warning: Invalid serial number in $file"
        continue
    fi

    # Calculate the reversed serial number
    reversed_serial=$(printf "%05d" $((total_files + 1 - current_serial)))
    
    # Create the new filename
    new_filename="${file//$current_serial/R$reversed_serial}"
    
    # Store rename operation
    renames+=("$file -> $new_filename")
done

# Print or execute renames
if [ "$dry_run" = true ]; then
    echo "Dry run - proposed renames:"
    for rename in "${renames[@]}"; do
        echo "$rename"
    done
else
    # Perform actual renames
    for rename in "${renames[@]}"; do
        # Split rename into old and new filename
        old_file=$(echo "$rename" | cut -d' ' -f1)
        new_file=$(echo "$rename" | cut -d' ' -f3)
        
        # Rename with error checking
        if ! mv "$old_file" "$new_file"; then
            echo "Error: Failed to rename $old_file to $new_file"
        fi
    done
fi