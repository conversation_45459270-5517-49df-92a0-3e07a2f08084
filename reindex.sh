#!/bin/bash

# Reverse-number PDF files in a directory
#
# This script renames PDF files with a PacPrem prefix and 5-digit serial number,
# reversing the numbering sequence. For example:
# PacPrem00001.pdf becomes PacPrem00012.pdf
# PacPrem00012.pdf becomes PacPrem00001.pdf
#
# Usage:
# ./rename_script.sh         # Rename files
# ./rename_script.sh -n      # Dry run (preview changes)
#
# Requirements:
# - Files must match pattern: PacPremXXXXX.pdf
# - 5-digit serial numbers
# - Run in directory containing target files

# Function to print usage
usage() {
    echo "Usage: $0 [-n|--dry-run] [-h|--help]"
    echo "  -n, --dry-run   Show what would be renamed without making changes"
    echo "  -h, --help      Show this help message"
    exit 1
}

# Default to actual renaming
dry_run=false

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--dry-run)
            dry_run=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "Unknown option: $1"
            usage
            ;;
    esac
done

# Check if PDF files exist
if ! ls PacPrem*.pdf 1> /dev/null 2>&1; then
    echo "Error: No PDF files found matching PacPrem*.pdf"
    exit 1
fi

# Get all matching files and extract serial numbers
declare -a files=()
declare -a serials=()

for file in PacPrem*.pdf; do
    # Extract serial number using parameter expansion (macOS compatible)
    if [[ "$file" =~ PacPrem([0-9]{5})\.pdf$ ]]; then
        serial="${BASH_REMATCH[1]}"
        files+=("$file")
        serials+=("$serial")
    else
        echo "Warning: Skipping file $file - doesn't match expected pattern"
    fi
done

# Check if we have any valid files
if [ ${#files[@]} -eq 0 ]; then
    echo "Error: No valid files found matching PacPremXXXXX.pdf pattern"
    exit 1
fi

# Sort serials to find min and max
sorted_serials=($(printf '%s\n' "${serials[@]}" | sort -n))
min_serial=${sorted_serials[0]}
max_serial=${sorted_serials[-1]}

echo "Found ${#files[@]} files with serials from $min_serial to $max_serial"

# Create temporary directory for safe renaming
temp_dir=$(mktemp -d)
trap "rm -rf '$temp_dir'" EXIT

if [ "$dry_run" = true ]; then
    echo "Dry run - proposed renames:"
fi

# Perform renaming in two phases to avoid conflicts
# Phase 1: Move all files to temporary names
for i in "${!files[@]}"; do
    file="${files[$i]}"
    serial="${serials[$i]}"
    
    # Calculate reversed serial
    reversed_serial=$(printf "%05d" $((min_serial + max_serial - serial)))
    
    # Create new filename
    new_filename="PacPrem${reversed_serial}.pdf"
    temp_filename="$temp_dir/$(basename "$file")"
    
    if [ "$dry_run" = true ]; then
        echo "$file -> $new_filename"
    else
        # Move to temp directory first
        if ! mv "$file" "$temp_filename"; then
            echo "Error: Failed to move $file to temporary location"
            exit 1
        fi
        echo "$file staged for rename to $new_filename"
    fi
done

# Phase 2: Move files back with new names (only if not dry run)
if [ "$dry_run" = false ]; then
    echo "Applying final renames..."
    for i in "${!files[@]}"; do
        file="${files[$i]}"
        serial="${serials[$i]}"
        reversed_serial=$(printf "%05d" $((min_serial + max_serial - serial)))
        new_filename="PacPrem${reversed_serial}.pdf"
        temp_filename="$temp_dir/$(basename "$file")"
        
        if ! mv "$temp_filename" "$new_filename"; then
            echo "Error: Failed to rename to $new_filename"
            exit 1
        else
            echo "✓ $file -> $new_filename"
        fi
    done
    echo "All files renamed successfully!"
fi